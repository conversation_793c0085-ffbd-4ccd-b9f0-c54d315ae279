# PBF章节信息处理脚本

这个JavaScript脚本可以自动将PBF文件中的章节信息添加到对应的视频文件中。

## 功能特点

- 🔍 自动扫描当前目录及子目录中的所有PBF文件
- 🎬 自动查找对应的视频文件（支持多种格式：mp4, mkv, avi, mov, wmv, flv, webm）
- 📖 解析PBF文件中的章节信息（支持UTF-16和UTF-8编码）
- ⏭️ 智能跳过已有章节信息的视频文件
- ✅ 使用FFmpeg为视频添加章节信息
- 🛡️ 保留原始视频文件，生成带章节的新文件

## 系统要求

- Node.js
- FFmpeg（用于处理视频文件）

## 使用方法

1. 将脚本文件 `process-chapters.js` 放在包含PBF和视频文件的目录中
2. 打开命令行，切换到该目录
3. 运行脚本：

```bash
node process-chapters.js
```

## 文件结构示例

```
video/
├── video1.mp4
├── video1.pbf
├── video2.mp4
├── video2.pbf
└── subfolder/
    ├── video3.mp4
    └── video3.pbf
```

## 输出

脚本会为每个没有章节信息的视频文件生成一个新的带章节的文件，文件名格式为：
`原文件名_with_chapters.扩展名`

例如：
- `video1.mp4` → `video1_with_chapters.mp4`

## PBF文件格式

脚本支持以下格式的PBF文件：

```
[Bookmark]
0=1674*书签 1*其他数据
1=5651*书签 2*其他数据
2=
```

其中：
- `0`, `1`, `2` 是章节序号
- `1674`, `5651` 是时间戳（毫秒）
- `书签 1`, `书签 2` 是章节标题

## 注意事项

- 脚本会自动跳过已有章节信息的视频文件
- 原始视频文件不会被修改
- 如果视频文件很大，处理可能需要一些时间
- 确保有足够的磁盘空间存储新生成的视频文件

## 错误处理

脚本包含完善的错误处理机制：
- 自动检测PBF文件编码（UTF-16/UTF-8）
- 验证章节信息的有效性
- 处理文件读写错误
- 显示详细的处理进度和结果统计

## 示例输出

```
🔍 开始扫描PBF文件...
📁 找到 3 个PBF文件

📄 处理: C:\video\video1.pbf
🎬 找到对应视频: C:\video\video1.mp4
📖 解析到 2 个章节:
   1. 00:00:01 - 书签 1
   2. 00:00:05 - 书签 2
✅ 成功为 C:\video\video1.mp4 添加章节信息
   输出文件: C:\video\video1_with_chapters.mp4

🎉 处理完成!
   ✅ 成功处理了 2 个视频文件
   ⏭️ 跳过了 1 个已有章节的视频文件
```
