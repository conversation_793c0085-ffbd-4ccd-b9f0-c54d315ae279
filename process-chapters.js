const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 支持的视频文件扩展名
const VIDEO_EXTENSIONS = ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm'];

// 解析PBF文件中的章节信息
function parsePBFFile(pbfPath) {
    try {
        // 尝试不同的编码方式读取文件
        let content;
        const buffer = fs.readFileSync(pbfPath);

        // 检查是否是UTF-16编码（BOM: FF FE）
        if (buffer[0] === 0xFF && buffer[1] === 0xFE) {
            content = buffer.toString('utf16le');
        } else {
            content = buffer.toString('utf8');
        }

        const lines = content.split(/\r?\n/);
        const chapters = [];

        for (const line of lines) {
            const trimmed = line.trim();
            // 检查是否是章节行：数字=时间戳*标题*其他数据
            if (trimmed && /^\d+=\d+\*/.test(trimmed)) {
                // 解析格式: 序号 = 时间戳 * 标题 * 其他数据
                const parts = trimmed.split('=');
                if (parts.length >= 2) {
                    const rightPart = parts[1].trim();
                    const starParts = rightPart.split('*');
                    if (starParts.length >= 2) {
                        const timeStr = starParts[0].trim();
                        const title = starParts[1].trim();

                        // 将时间戳转换为秒
                        const timeInSeconds = parseInt(timeStr) / 1000;

                        if (!isNaN(timeInSeconds) && title && title !== '') {
                            chapters.push({
                                time: timeInSeconds,
                                title: title
                            });
                        }
                    }
                }
            }
        }

        // 按时间排序
        chapters.sort((a, b) => a.time - b.time);
        return chapters;
    } catch (error) {
        console.error(`解析PBF文件失败: ${pbfPath}`, error.message);
        return [];
    }
}

// 检查视频文件是否已有章节信息
function hasChapters(videoPath) {
    try {
        const result = execSync(`ffprobe -v quiet -print_format json -show_chapters "${videoPath}"`,
            { encoding: 'utf8' });
        const data = JSON.parse(result);
        return data.chapters && data.chapters.length > 0;
    } catch (error) {
        console.error(`检查章节信息失败: ${videoPath}`, error.message);
        return false;
    }
}

// 创建章节文件
function createChapterFile(chapters, chapterFilePath) {
    let content = ';FFMETADATA1\n';

    for (let i = 0; i < chapters.length; i++) {
        const chapter = chapters[i];
        const nextChapter = chapters[i + 1];

        const startTime = Math.floor(chapter.time * 1000); // 转换为毫秒
        const endTime = nextChapter ? Math.floor(nextChapter.time * 1000) : null;

        content += '\n[CHAPTER]\n';
        content += 'TIMEBASE=1/1000\n';
        content += `START=${startTime}\n`;
        if (endTime !== null) {
            content += `END=${endTime}\n`;
        }
        content += `title=${chapter.title}\n`;
    }

    // 使用UTF-8编码写入文件
    fs.writeFileSync(chapterFilePath, content, { encoding: 'utf8' });
}

// 为视频文件添加章节
function addChaptersToVideo(videoPath, chapters) {
    try {
        const videoDir = path.dirname(videoPath);
        const videoName = path.basename(videoPath, path.extname(videoPath));
        const chapterFile = path.join(videoDir, `${videoName}_chapters.txt`);
        const outputVideo = path.join(videoDir, `${videoName}_with_chapters${path.extname(videoPath)}`);

        // 创建章节文件
        createChapterFile(chapters, chapterFile);

        // 使用FFmpeg添加章节
        const command = `ffmpeg -i "${videoPath}" -i "${chapterFile}" -map_metadata 1 -codec copy "${outputVideo}"`;
        console.log(`执行命令: ${command}`);

        execSync(command, { stdio: 'inherit' });

        // 删除临时章节文件
        fs.unlinkSync(chapterFile);

        console.log(`✅ 成功为 ${videoPath} 添加章节信息`);
        console.log(`   输出文件: ${outputVideo}`);

    } catch (error) {
        console.error(`❌ 为视频添加章节失败: ${videoPath}`, error.message);
    }
}

// 递归扫描目录查找PBF文件
function findPBFFiles(dir) {
    const pbfFiles = [];

    try {
        const items = fs.readdirSync(dir);

        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);

            if (stat.isDirectory()) {
                pbfFiles.push(...findPBFFiles(fullPath));
            } else if (path.extname(item).toLowerCase() === '.pbf') {
                pbfFiles.push(fullPath);
            }
        }
    } catch (error) {
        console.error(`扫描目录失败: ${dir}`, error.message);
    }

    return pbfFiles;
}

// 查找对应的视频文件
function findCorrespondingVideo(pbfPath) {
    const dir = path.dirname(pbfPath);
    const baseName = path.basename(pbfPath, '.pbf');

    for (const ext of VIDEO_EXTENSIONS) {
        const videoPath = path.join(dir, baseName + ext);
        if (fs.existsSync(videoPath)) {
            return videoPath;
        }
    }

    return null;
}

// 主函数
function main() {
    console.log('🔍 开始扫描PBF文件...');

    const currentDir = process.cwd();
    const pbfFiles = findPBFFiles(currentDir);

    console.log(`📁 找到 ${pbfFiles.length} 个PBF文件`);

    if (pbfFiles.length === 0) {
        console.log('❌ 未找到任何PBF文件');
        return;
    }

    let processedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const pbfPath of pbfFiles) {
        console.log(`\n📄 处理: ${pbfPath}`);

        try {
            // 查找对应的视频文件
            const videoPath = findCorrespondingVideo(pbfPath);
            if (!videoPath) {
                console.log(`⚠️  未找到对应的视频文件`);
                continue;
            }

            console.log(`🎬 找到对应视频: ${videoPath}`);

            // 检查视频是否已有章节信息
            if (hasChapters(videoPath)) {
                console.log(`⏭️  视频已有章节信息，跳过`);
                skippedCount++;
                continue;
            }

            // 解析PBF文件
            const chapters = parsePBFFile(pbfPath);
            if (chapters.length === 0) {
                console.log(`⚠️  PBF文件中未找到有效章节信息`);
                continue;
            }

            console.log(`📖 解析到 ${chapters.length} 个章节:`);
            chapters.forEach((chapter, index) => {
                const timeStr = new Date(chapter.time * 1000).toISOString().substr(11, 8);
                console.log(`   ${index + 1}. ${timeStr} - ${chapter.title}`);
            });

            // 添加章节到视频
            addChaptersToVideo(videoPath, chapters);
            processedCount++;

        } catch (error) {
            console.error(`❌ 处理文件时出错: ${pbfPath}`, error.message);
            errorCount++;
        }
    }

    console.log(`\n🎉 处理完成!`);
    console.log(`   ✅ 成功处理了 ${processedCount} 个视频文件`);
    console.log(`   ⏭️  跳过了 ${skippedCount} 个已有章节的视频文件`);
    if (errorCount > 0) {
        console.log(`   ❌ ${errorCount} 个文件处理失败`);
    }
}

// 运行主函数
if (require.main === module) {
    main();
}
